# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive JWT-based authentication and authorization microservice built with Spring Boot 3.5.4 and Java 21 for the GEDSYS platform. It provides secure user authentication, session management, role-based access control, mobile integration, automatic username generation, and complete CI/CD pipeline with Docker deployment.

## Development Commands

### Build and Test
```bash
# Run the application (development with Docker Compose)
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev

# Clean build
./mvnw clean package

# Run tests (currently no tests implemented)
./mvnw test

# Build optimized Docker image (multi-stage)
docker build -t gedsys2-authentication .

# Build with Spring Boot (layered)
./mvnw spring-boot:build-image
```

### Database Operations
```bash
# Start development database
docker-compose up -d

# Check Flyway migration status
./mvnw flyway:info

# Flyway migration (manual, usually automatic on startup)
./mvnw flyway:migrate
```

### CI/CD and Docker
```bash
# Build local Docker image for testing
docker build -t gedsys2-auth:local .

# Run container locally
docker run -p 8080:8080 gedsys2-auth:local

# Check health status
curl http://localhost:8080/actuator/health

# Check application logs
docker logs <container-id>
```

## Architecture

### Core Components
- **AuthenticationService**: JWT token generation, validation, flexible login (username/email)
- **UserService**: User CRUD operations, automatic username generation, logical deletion
- **JwtService**: Token lifecycle management, session-aware expiration
- **PushTokenService**: Mobile device token management (FCM/APNS)
- **UsernameGenerator**: Automatic unique username creation from names
- **RefreshTokenService**: Refresh token rotation and session management

### Database Structure
- **User**: Main user entity with status management (ACTIVE, INACTIVE, DELETED)
- **RefreshToken**: Single-use token rotation with session tracking  
- **PushToken**: Mobile device notification tokens
- **Flyway Migrations**: V1-V8 covering complete schema evolution including username uniqueness

### Security Features
- Single session policy per session type (MOBILE/WEB)
- Role-based access control (USER/ADMIN)
- Session invalidation on password changes
- Logical deletion preserving audit trails
- Custom authentication entry point with localized error messages

## Configuration

### Environment Variables
```bash
# Database
DB_URL=**************************************************
DB_USERNAME=auth_user
DB_PASSWORD=auth_password

# JWT
JWT_SECRET=your-secret-key-here
JWT_ACCESS_EXPIRATION=3600000
JWT_MOBILE_ACCESS_EXPIRATION=7200000
JWT_WEB_ACCESS_EXPIRATION=3600000
JWT_REFRESH_EXPIRATION=604800000
```

### Application Profiles
- `dev`: Development with Docker Compose PostgreSQL
- `test`: Testing with H2 database
- Default: Production configuration

## API Endpoints

### Authentication
- `POST /auth/login` - Flexible login (username or email)
- `POST /auth/refresh` - Token refresh
- `POST /auth/logout` - Session logout
- `GET /auth/profile` - User profile
- `PUT /auth/change-password` - Password change

### Admin Operations (ADMIN role required)
- `GET /admin/users` - List users with pagination/filtering
- `POST /admin/users` - Create user
- `GET /admin/users/{id}` - Get user details
- `PUT /admin/users/{id}` - Update user

### Mobile Integration
- `POST /auth/push-token` - Register push token
- `GET /auth/push-token` - Get push token info

### Documentation
- Swagger UI: `http://localhost:8080/swagger-ui.html`
- API docs: `http://localhost:8080/v3/api-docs`
- OpenAPI specifications: `docs/openapi/` directory
  - `authentication.openapi.json` - Authentication endpoints
  - `admin.openapi.json` - Admin user management
  - `admin-session.openapi.json` - Admin session management
  - `health.openapi.json` - Health check endpoints
  - `push-token.openapi.json` - Push token management

## Project Structure

```
├── .github/workflows/                     # CI/CD pipelines
│   └── deploy.yml                        # Main deployment workflow
├── .claude/commands/                      # Claude Code commands
│   ├── openapi-expert.md                # OpenAPI management agent
│   └── update-claudemd.md               # CLAUDE.md update command
├── docs/                                 # Documentation
│   ├── openapi/                         # OpenAPI specifications
│   │   ├── authentication.openapi.json  # Auth endpoints
│   │   ├── admin.openapi.json           # Admin user management
│   │   ├── admin-session.openapi.json   # Admin session management
│   │   ├── health.openapi.json          # Health endpoints
│   │   └── push-token.openapi.json      # Push token management
│   └── CI-CD-SETUP.md                   # CI/CD configuration guide
├── src/main/java/co/com/gedsys/authentication/
│   ├── AuthenticationApplication.java    # Main application class
│   ├── config/                          # Configuration classes
│   │   ├── ApplicationStartupValidator.java # Startup validation
│   │   ├── CustomAuthenticationEntryPoint.java # Custom auth entry point
│   │   ├── JwtAuthenticationFilter.java # JWT filter
│   │   ├── JwtProperties.java           # JWT configuration properties
│   │   ├── MetricsConfiguration.java    # Metrics config
│   │   ├── SecurityBeanConfig.java      # Security beans
│   │   ├── SecurityConfiguration.java   # Main security config
│   │   └── SwaggerConfig.java           # API documentation config
│   ├── controller/                      # REST controllers
│   │   ├── AdminController.java         # Admin operations
│   │   ├── AuthenticationController.java # Authentication endpoints
│   │   ├── HealthController.java        # Health checks
│   │   └── PushTokenController.java     # Push token management
│   ├── dto/                             # Data transfer objects
│   ├── entity/                          # JPA entities
│   │   ├── DeviceType.java              # Device type enum
│   │   ├── Role.java                    # User role enum
│   │   ├── SessionType.java             # Session type enum
│   │   ├── UserStatus.java              # User status enum
│   │   ├── User.java                    # User entity
│   │   ├── RefreshToken.java            # Refresh token entity
│   │   └── PushToken.java               # Push token entity
│   ├── exception/                       # Custom exceptions and handlers
│   ├── repository/                      # Data access layer
│   └── service/                         # Business logic layer
│       ├── AuthenticationService.java   # Authentication logic
│       ├── JwtService.java              # JWT operations
│       ├── PushTokenService.java        # Push token management
│       ├── RefreshTokenService.java     # Refresh token operations
│       ├── UserService.java             # User operations
│       └── UsernameGenerator.java       # Username generation
├── src/main/resources/
│   └── logback-spring.xml              # Enhanced logging configuration
├── Dockerfile                          # Multi-stage optimized Docker image
├── .dockerignore                       # Docker build context exclusions
└── docker-compose.yml                 # Development database setup
```

## Testing Strategy

**Note**: Actualmente no hay tests implementados en el proyecto. La estructura de testing descrita en el README es aspiracional.

### Test Structure (Planned)
- Unit tests para servicios y lógica de negocio
- Integration tests con Testcontainers para PostgreSQL
- Controller tests para endpoints REST

## Development Guidelines

### Code Conventions
- Java 21 modern features
- Spring Boot 3.5.4 patterns  
- Security-first approach
- Comprehensive error handling in Spanish
- Logical deletion over physical deletion

### Database Best Practices
- Use Flyway migrations for schema changes (V1-V8 currently implemented)
- Include deleted users in uniqueness checks
- Maintain audit trails with UserStatus enum

### Security Considerations
- Never expose JWT secrets in code
- Validate session types appropriately (MOBILE/WEB)
- Custom authentication entry point with localized messages
- Method-level security with @PreAuthorize annotations

## Recent Updates (Updated: 2025-01-18)

### CI/CD Pipeline Integration
- **GitHub Actions workflow** implementado en `.github/workflows/deploy.yml`
- **Docker multi-stage** optimizado con Alpine Linux para producción
- **Pipeline stages**: build, security scan (SpotBugs/FindSecBugs), Docker build multi-arch, deployment
- **Registry**: Configurado para `registry.gedsys.dev` con secrets management

### Enhanced Documentation
- **OpenAPI specifications** completas en `docs/openapi/`:
  - Documentación detallada para todos los endpoints
  - Ejemplos de request/response
  - Esquemas de validación completos
- **CI/CD Setup Guide** en `docs/CI-CD-SETUP.md`

### Logging and Monitoring Improvements
- **Enhanced logback configuration** con perfiles dev/prod
- **Health checks** integrados en Docker
- **Metrics configuration** optimizada para observabilidad

### Security Enhancements
- **SpotBugs + FindSecBugs** análisis estático integrado en CI
- **Non-root user** en imagen Docker
- **Secrets management** con GitHub Actions

## Default Admin Account
- Email: `<EMAIL>`
- Username: `admin` (auto-generated)
- Password: `admin123`
- Role: ADMIN
- Change immediately in production

## CI/CD Deployment

### Required Secrets
Configure en GitHub Settings → Secrets:
- `REGISTRY_USERNAME`: Usuario para registry.gedsys.dev
- `REGISTRY_PASSWORD`: Token de acceso al registry

### Deployment Strategy
- **Automatic deployment** en push a `main` branch
- **Manual approval** requerido para environment "production"
- **Multi-architecture images** (AMD64/ARM64)
- **Rolling updates** con health checks
- @changelog.md contiene el historial de cambios para consulta rapida
- Apply No comments policy in code source and clean code naming convention