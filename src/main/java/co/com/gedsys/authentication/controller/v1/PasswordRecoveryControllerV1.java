package co.com.gedsys.authentication.controller.v1;

import co.com.gedsys.authentication.dto.PasswordRecoveryCodeResponse;
import co.com.gedsys.authentication.dto.PasswordRecoveryErrorResponse;
import co.com.gedsys.authentication.dto.PasswordResetRequest;
import co.com.gedsys.authentication.dto.PasswordResetResponse;
import co.com.gedsys.authentication.entity.PasswordRecoveryCode;
import co.com.gedsys.authentication.exception.*;
import co.com.gedsys.authentication.service.PasswordRecoveryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/password-recovery")
@Tag(name = "Password Recovery", description = "Password recovery operations")
public class PasswordRecoveryControllerV1 {

    private static final Logger logger = LoggerFactory.getLogger(PasswordRecoveryControllerV1.class);

    private final PasswordRecoveryService passwordRecoveryService;

    public PasswordRecoveryControllerV1(PasswordRecoveryService passwordRecoveryService) {
        this.passwordRecoveryService = passwordRecoveryService;
    }

    @PostMapping("/{identifier}/code")
    @Operation(summary = "Generate password recovery code", 
               description = "Generates a secure recovery code for password reset. The identifier can be email or username.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Recovery code generated successfully",
                    content = @Content(mediaType = "application/json", 
                                     schema = @Schema(implementation = PasswordRecoveryCodeResponse.class),
                                     examples = @ExampleObject(value = """
                                          {
                                            "success": true,
                                            "data": {
                                              "code": "A1B2C3D4",
                                              "email": "<EMAIL>",
                                              "expiresAt": "2025-09-24T15:00:00Z",
                                              "createdAt": "2025-09-24T14:00:00Z"
                                            }
                                          }
                                          """))),
        @ApiResponse(responseCode = "404", description = "User not found",
                    content = @Content(mediaType = "application/json",
                                     schema = @Schema(implementation = PasswordRecoveryErrorResponse.class))),
        @ApiResponse(responseCode = "403", description = "User inactive",
                    content = @Content(mediaType = "application/json",
                                     schema = @Schema(implementation = PasswordRecoveryErrorResponse.class))),
        @ApiResponse(responseCode = "429", description = "Rate limit exceeded",
                    content = @Content(mediaType = "application/json",
                                     schema = @Schema(implementation = PasswordRecoveryErrorResponse.class)))
    })
    public ResponseEntity<?> generateRecoveryCode(
            @Parameter(description = "User identifier (email or username)", required = true)
            @PathVariable String identifier) {
        
        logger.debug("Solicitud de generación de código para: {}", identifier);
        
        try {
            PasswordRecoveryCode recoveryCode = passwordRecoveryService.generateRecoveryCode(identifier);
            
            PasswordRecoveryCodeResponse response = new PasswordRecoveryCodeResponse(
                recoveryCode.getCode(),
                recoveryCode.getUser().getEmail(),
                recoveryCode.getExpiresAt(),
                recoveryCode.getCreatedAt()
            );
            
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
            
        } catch (UserNotFoundException e) {
            logger.warn("Usuario no encontrado: {}", identifier);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new PasswordRecoveryErrorResponse("USER_NOT_FOUND", e.getMessage()));
                    
        } catch (UserInactiveException e) {
            logger.warn("Usuario inactivo: {}", identifier);
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(new PasswordRecoveryErrorResponse("USER_INACTIVE", e.getMessage()));
                    
        } catch (RateLimitExceededException e) {
            logger.warn("Límite de intentos excedido para: {}", identifier);
            return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                    .body(new PasswordRecoveryErrorResponse("RATE_LIMIT_EXCEEDED", e.getMessage()));
                    
        } catch (Exception e) {
            logger.error("Error inesperado al generar código para: {}", identifier, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new PasswordRecoveryErrorResponse("INTERNAL_ERROR", 
                            "Error interno del servidor"));
        }
    }

    @PatchMapping("/{identifier}")
    @Operation(summary = "Reset password with recovery code",
               description = "Validates the recovery code and updates the user password")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Password reset successfully",
                    content = @Content(mediaType = "application/json",
                                     schema = @Schema(implementation = PasswordResetResponse.class),
                                     examples = @ExampleObject(value = """
                                         {
                                           "success": true,
                                           "message": "Contraseña actualizada exitosamente",
                                           "timestamp": "2025-09-24T14:00:00Z"
                                         }
                                         """))),
        @ApiResponse(responseCode = "400", description = "Invalid, expired, or used code",
                    content = @Content(mediaType = "application/json",
                                     schema = @Schema(implementation = PasswordRecoveryErrorResponse.class),
                                     examples = {
                                         @ExampleObject(name = "Invalid Code", value = """
                                             {
                                               "success": false,
                                               "error": {
                                                 "code": "INVALID_CODE",
                                                 "message": "El código proporcionado no es válido",
                                                 "timestamp": "2025-09-24T14:00:00Z"
                                               }
                                             }
                                             """),
                                         @ExampleObject(name = "Expired Code", value = """
                                             {
                                               "success": false,
                                               "error": {
                                                 "code": "EXPIRED_CODE",
                                                 "message": "El código de recuperación ha expirado",
                                                 "timestamp": "2025-09-24T14:00:00Z"
                                               }
                                             }
                                             """)
                                     }))
    })
    public ResponseEntity<?> resetPassword(
            @Parameter(description = "User identifier (email or username)", required = true)
            @PathVariable String identifier,
            @Valid @RequestBody PasswordResetRequest request) {
        
        logger.debug("Solicitud de cambio de contraseña para: {}", identifier);
        
        try {
            passwordRecoveryService.validateAndResetPassword(
                identifier, 
                request.getCode(), 
                request.getNewPassword()
            );
            
            PasswordResetResponse response = new PasswordResetResponse("Contraseña actualizada exitosamente");
            return ResponseEntity.ok(response);
            
        } catch (InvalidRecoveryCodeException e) {
            logger.warn("Código inválido para usuario: {}", identifier);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new PasswordRecoveryErrorResponse("INVALID_CODE", e.getMessage()));
                    
        } catch (ExpiredRecoveryCodeException e) {
            logger.warn("Código expirado para usuario: {}", identifier);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new PasswordRecoveryErrorResponse("EXPIRED_CODE", e.getMessage()));
                    
        } catch (UsedRecoveryCodeException e) {
            logger.warn("Código ya usado para usuario: {}", identifier);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new PasswordRecoveryErrorResponse("USED_CODE", e.getMessage()));
                    
        } catch (Exception e) {
            logger.error("Error inesperado al cambiar contraseña para: {}", identifier, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new PasswordRecoveryErrorResponse("INTERNAL_ERROR", 
                            "Error interno del servidor"));
        }
    }
}