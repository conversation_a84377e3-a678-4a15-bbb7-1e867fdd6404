package co.com.gedsys.authentication.util;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Component
public class RateLimiter {

    private static final int MAX_ATTEMPTS = 3;
    private static final int TIME_WINDOW_MINUTES = 15;
    
    private final ConcurrentMap<String, AttemptRecord> attempts = new ConcurrentHashMap<>();

    public boolean isAllowed(String identifier) {
        cleanup();
        
        AttemptRecord record = attempts.get(identifier);
        
        if (record == null) {
            return true;
        }
        
        if (isRecordExpired(record)) {
            attempts.remove(identifier);
            return true;
        }
        
        return record.count < MAX_ATTEMPTS;
    }

    public void recordAttempt(String identifier) {
        cleanup();
        
        attempts.compute(identifier, (key, existingRecord) -> {
            if (existingRecord == null || isRecordExpired(existingRecord)) {
                return new AttemptRecord(LocalDateTime.now(), 1);
            } else {
                return new AttemptRecord(existingRecord.firstAttemptTime, existingRecord.count + 1);
            }
        });
    }

    public int getRemainingAttempts(String identifier) {
        cleanup();
        
        AttemptRecord record = attempts.get(identifier);
        
        if (record == null || isRecordExpired(record)) {
            return MAX_ATTEMPTS;
        }
        
        return Math.max(0, MAX_ATTEMPTS - record.count);
    }

    public LocalDateTime getResetTime(String identifier) {
        cleanup();
        
        AttemptRecord record = attempts.get(identifier);
        
        if (record == null || isRecordExpired(record)) {
            return LocalDateTime.now();
        }
        
        return record.firstAttemptTime.plus(TIME_WINDOW_MINUTES, ChronoUnit.MINUTES);
    }

    private boolean isRecordExpired(AttemptRecord record) {
        LocalDateTime expireTime = record.firstAttemptTime.plus(TIME_WINDOW_MINUTES, ChronoUnit.MINUTES);
        return LocalDateTime.now().isAfter(expireTime);
    }

    private void cleanup() {
        LocalDateTime now = LocalDateTime.now();
        attempts.entrySet().removeIf(entry -> {
            AttemptRecord record = entry.getValue();
            LocalDateTime expireTime = record.firstAttemptTime.plus(TIME_WINDOW_MINUTES, ChronoUnit.MINUTES);
            return now.isAfter(expireTime);
        });
    }

    private static class AttemptRecord {
        final LocalDateTime firstAttemptTime;
        final int count;

        AttemptRecord(LocalDateTime firstAttemptTime, int count) {
            this.firstAttemptTime = firstAttemptTime;
            this.count = count;
        }
    }
}