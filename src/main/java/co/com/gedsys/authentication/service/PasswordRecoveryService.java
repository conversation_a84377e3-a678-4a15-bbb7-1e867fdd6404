package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.entity.*;
import co.com.gedsys.authentication.exception.*;
import co.com.gedsys.authentication.repository.PasswordRecoveryCodeRepository;
import co.com.gedsys.authentication.repository.UserRepository;
import co.com.gedsys.authentication.util.HttpRequestContextUtil;
import co.com.gedsys.authentication.util.RateLimiter;
import co.com.gedsys.authentication.util.SecureCodeGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
@Transactional
public class PasswordRecoveryService {

    private static final Logger logger = LoggerFactory.getLogger(PasswordRecoveryService.class);

    private final UserRepository userRepository;
    private final PasswordRecoveryCodeRepository passwordRecoveryCodeRepository;
    private final SecurityAuditService securityAuditService;
    private final SecureCodeGenerator secureCodeGenerator;
    private final RateLimiter rateLimiter;
    private final PasswordEncoder passwordEncoder;
    private final RefreshTokenService refreshTokenService;

    @Value("${password-recovery.code.expiration-minutes:60}")
    private int codeExpirationMinutes;

    public PasswordRecoveryService(UserRepository userRepository,
                                 PasswordRecoveryCodeRepository passwordRecoveryCodeRepository,
                                 SecurityAuditService securityAuditService,
                                 SecureCodeGenerator secureCodeGenerator,
                                 RateLimiter rateLimiter,
                                 PasswordEncoder passwordEncoder,
                                 RefreshTokenService refreshTokenService) {
        this.userRepository = userRepository;
        this.passwordRecoveryCodeRepository = passwordRecoveryCodeRepository;
        this.securityAuditService = securityAuditService;
        this.secureCodeGenerator = secureCodeGenerator;
        this.rateLimiter = rateLimiter;
        this.passwordEncoder = passwordEncoder;
        this.refreshTokenService = refreshTokenService;
    }

    @Transactional
    public PasswordRecoveryCode generateRecoveryCode(String identifier) {
        logger.debug("Iniciando generación de código de recuperación para: {}", identifier);

        // Verificar rate limiting
        if (!rateLimiter.isAllowed(identifier)) {
            logger.warn("Límite de intentos excedido para: {}", identifier);
            securityAuditService.logPasswordRecoveryRateLimited(identifier);
            rateLimiter.recordAttempt(identifier);
            throw RateLimitExceededException.forIdentifier(identifier);
        }

        // Buscar usuario por email o username
        Optional<User> userOpt = findUserByEmailOrUsername(identifier);
        if (userOpt.isEmpty()) {
            logger.warn("Usuario no encontrado: {}", identifier);
            rateLimiter.recordAttempt(identifier);
            throw new UserNotFoundException("Usuario no encontrado: " + identifier);
        }

        User user = userOpt.get();

        // Verificar que el usuario esté activo
        if (user.getStatus() != UserStatus.ACTIVE) {
            logger.warn("Intento de recuperación para usuario inactivo: {} (status: {})", 
                       identifier, user.getStatus());
            securityAuditService.logPasswordRecoveryFailed(user, "Usuario inactivo", null);
            rateLimiter.recordAttempt(identifier);
            throw UserInactiveException.forIdentifier(identifier);
        }

        // Registrar intento en rate limiter
        rateLimiter.recordAttempt(identifier);

        // Registrar solicitud en auditoría
        securityAuditService.logPasswordRecoveryRequest(user, identifier);

        // Invalidar códigos previos del usuario
        invalidatePreviousCodes(user);

        // Generar nuevo código
        String code = secureCodeGenerator.generateUniqueCode();
        LocalDateTime expiresAt = LocalDateTime.now().plusMinutes(codeExpirationMinutes);

        PasswordRecoveryCode recoveryCode = new PasswordRecoveryCode(user, code, expiresAt);
        
        // Capturar información de la request
        try {
            recoveryCode.setRequestIp(HttpRequestContextUtil.getClientIpAddress());
            // Para recovery code no usamos sessionId, se genera cuando se crea un refresh token
        } catch (Exception e) {
            logger.debug("No se pudo obtener información de la request: {}", e.getMessage());
        }

        PasswordRecoveryCode savedCode = passwordRecoveryCodeRepository.save(recoveryCode);

        // Registrar generación exitosa en auditoría
        securityAuditService.logPasswordRecoveryCodeGenerated(user, savedCode.getId().toString());

        logger.info("Código de recuperación generado exitosamente para usuario: {} (ID: {})", 
                   identifier, user.getId());

        return savedCode;
    }

    @Transactional
    public void validateAndResetPassword(String identifier, String code, String newPassword) {
        logger.debug("Iniciando validación de código y cambio de contraseña para: {}", identifier);

        // Buscar código activo
        Optional<PasswordRecoveryCode> codeOpt = passwordRecoveryCodeRepository.findByCodeAndUsedFalse(code);
        if (codeOpt.isEmpty()) {
            logger.warn("Código de recuperación no encontrado o ya usado: {}", code);
            securityAuditService.logPasswordRecoveryFailed(null, "Código inválido", code);
            throw new InvalidRecoveryCodeException();
        }

        PasswordRecoveryCode recoveryCode = codeOpt.get();
        User user = recoveryCode.getUser();

        // Verificar que el código pertenece al usuario correcto
        if (!identifier.equals(user.getEmail()) && !identifier.equals(user.getUsername())) {
            logger.warn("Intento de uso de código con identificador incorrecto. Código del usuario: {}, " +
                       "Identificador proporcionado: {}", user.getEmail(), identifier);
            markCodeAsUsed(recoveryCode);
            securityAuditService.logPasswordRecoveryFailed(user, "Identificador incorrecto", code);
            throw new InvalidRecoveryCodeException();
        }

        // Verificar si el código ha expirado
        if (recoveryCode.isExpired()) {
            logger.warn("Intento de uso de código expirado: {}", code);
            markCodeAsUsed(recoveryCode);
            securityAuditService.logPasswordRecoveryExpired(user, code);
            throw new ExpiredRecoveryCodeException();
        }

        // Verificar si el código ya fue usado
        if (recoveryCode.isUsed()) {
            logger.warn("Intento de uso de código ya utilizado: {}", code);
            securityAuditService.logPasswordRecoveryFailed(user, "Código ya usado", code);
            throw new UsedRecoveryCodeException();
        }

        try {
            // Marcar código como usado
            markCodeAsUsed(recoveryCode);

            // Cambiar la contraseña
            user.setPassword(passwordEncoder.encode(newPassword));
            userRepository.save(user);

            // Invalidar todos los refresh tokens del usuario
            refreshTokenService.revokeAllUserTokens(user.getId());

            // Registrar éxito en auditoría
            securityAuditService.logPasswordRecoverySuccess(user, code);

            logger.info("Contraseña cambiada exitosamente para usuario: {} (ID: {})", 
                       identifier, user.getId());

        } catch (Exception e) {
            logger.error("Error al cambiar contraseña para usuario: {}", identifier, e);
            securityAuditService.logPasswordRecoveryFailed(user, "Error interno", code);
            throw new RuntimeException("Error al procesar cambio de contraseña", e);
        }
    }

    private Optional<User> findUserByEmailOrUsername(String identifier) {
        // Intentar buscar por email primero
        Optional<User> userByEmail = userRepository.findByEmail(identifier);
        if (userByEmail.isPresent()) {
            return userByEmail;
        }

        // Si no se encuentra por email, buscar por username
        return userRepository.findByUsername(identifier);
    }

    private void invalidatePreviousCodes(User user) {
        passwordRecoveryCodeRepository.invalidateAllActiveCodesByUser(user, LocalDateTime.now());
        logger.debug("Códigos previos invalidados para usuario ID: {}", user.getId());
    }

    private void markCodeAsUsed(PasswordRecoveryCode recoveryCode) {
        recoveryCode.setUsed(true);
        recoveryCode.setUsedAt(LocalDateTime.now());
        
        // Capturar IP de uso
        try {
            recoveryCode.setUsageIp(HttpRequestContextUtil.getClientIpAddress());
        } catch (Exception e) {
            logger.debug("No se pudo obtener IP de uso: {}", e.getMessage());
        }
        
        passwordRecoveryCodeRepository.save(recoveryCode);
    }

    @Transactional(readOnly = true)
    public int getRemainingAttempts(String identifier) {
        return rateLimiter.getRemainingAttempts(identifier);
    }

    @Transactional(readOnly = true)
    public LocalDateTime getResetTime(String identifier) {
        return rateLimiter.getResetTime(identifier);
    }
}