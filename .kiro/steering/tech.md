# Technology Stack

## Core Framework
- **Spring Boot 3.5.4** - Main application framework
- **Java 21** - Programming language and runtime
- **Maven** - Build system and dependency management

## Key Dependencies
- **Spring Security** - Authentication and authorization
- **Spring Data JPA** - Database access layer
- **Spring Boot Validation** - Request validation
- **PostgreSQL** - Primary database
- **Flyway** - Database migration management
- **JJWT 0.12.6** - JWT token handling
- **HikariCP** - Connection pooling (via Spring Boot)
- **SpringDoc OpenAPI 2.8.9** - Interactive API documentation (Swagger UI)

## Testing Stack
- **Spring Boot Test** - Testing framework
- **Testcontainers 1.20.4** - Integration testing with PostgreSQL
- **H2 Database** - In-memory database for unit tests
- **Spring Security Test** - Security testing utilities

## Development Tools
- **Spring Boot DevTools** - Development productivity
- **Docker Compose** - Local development environment
- **<PERSON><PERSON>per** - Consistent build environment

## Common Commands

### Build and Run
```bash
# Build the project
./mvnw clean compile

# Run tests
./mvnw test

# Run the application
./mvnw spring-boot:run

# Package the application
./mvnw clean package
```

### Database Operations
```bash
# Run Flyway migrations
./mvnw flyway:migrate

# Validate migrations
./mvnw flyway:validate

# Get migration info
./mvnw flyway:info
```

### Docker Development
```bash
# Start PostgreSQL for development
docker-compose up -d

# Stop development services
docker-compose down
```

### API Documentation
```bash
# Access interactive Swagger UI
open http://localhost:8080/swagger-ui.html

# Get OpenAPI specification
curl http://localhost:8080/v3/api-docs
```

## Configuration
- Environment-based configuration using Spring profiles
- JWT settings configurable via environment variables
- Database connection via environment variables
- Flyway migrations in `src/main/resources/db/migration`