{"openapi": "3.0.3", "info": {"title": "GEDSYS Authentication API", "description": "API para autenticación, gestión de sesiones y perfil de usuario en GEDSYS", "version": "1.0.0", "contact": {"name": "GEDSYS Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080", "description": "<PERSON><PERSON><PERSON>"}], "tags": [{"name": "Autenticación", "description": "Endpoints para autenticación, gestión de sesiones y perfil de usuario"}], "paths": {"/identity/api/v1/auth/login": {"post": {"tags": ["Autenticación"], "summary": "In<PERSON><PERSON>", "description": "Autentica al usuario y crea una nueva sesión. Soporta sesiones móviles y web con registro opcional de token push.", "operationId": "login", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}, "examples": {"web-login": {"summary": "Login web", "value": {"identifier": "<EMAIL>", "password": "password123", "sessionType": "WEB"}}, "mobile-login": {"summary": "Login móvil con push token", "value": {"identifier": "john.doe", "password": "password123", "sessionType": "MOBILE", "pushToken": "fcm-token-example", "deviceType": "ANDROID", "deviceId": "device-123"}}}}}}, "responses": {"200": {"description": "Autenticación exitosa", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}, "example": {"accessToken": "eyJhbGciOiJIUzI1NiIs...", "refreshToken": "eyJhbGciOiJIUzI1NiIs...", "tokenType": "Bearer", "expiresIn": 3600, "user": {"id": 1, "email": "<EMAIL>", "username": "john.doe", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "role": "USER", "createdAt": "2024-01-01T12:00:00Z"}}}}}, "400": {"description": "Datos de entrada inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Credenciales incorrectas", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "<PERSON><PERSON><PERSON> deshabili<PERSON>o", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/auth/refresh": {"post": {"tags": ["Autenticación"], "summary": "Renovar tokens", "description": "Valida y consume el refresh token para generar nuevos tokens JWT. Los refresh tokens son de un solo uso.", "operationId": "refreshToken", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}}, "responses": {"200": {"description": "Tokens renovados exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "400": {"description": "Refresh token inválido o expirado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Refresh token no válido", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/auth/logout": {"post": {"tags": ["Autenticación"], "summary": "<PERSON><PERSON><PERSON>", "description": "Invalida los tokens de refresh y elimina tokens push según el tipo de sesión. Requiere autenticación.", "operationId": "logout", "security": [{"BearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"sessionType": {"type": "string", "enum": ["MOBILE", "WEB"], "description": "Tipo de sesión a cerrar. Si no se especifica, cierra todas las sesiones."}}}}}}, "responses": {"200": {"description": "Sesión cerrada exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"message": "Logout successful"}}}}, "400": {"description": "Tipo de sesión inválido", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/auth/profile": {"get": {"tags": ["Autenticación"], "summary": "Obtener perfil de usuario", "description": "Retorna la información del perfil del usuario autenticado actualmente.", "operationId": "getProfile", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Perfil obtenido exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponse"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/auth/change-password": {"put": {"tags": ["Autenticación"], "summary": "Cambiar contraseña", "description": "Cambia la contraseña del usuario autenticado. Invalida todas las sesiones del usuario después del cambio exitoso.", "operationId": "changePassword", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "Contraseña cambiada exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"message": "Password changed successfully. Please login again."}}}}, "400": {"description": "Datos de entrada inválidos o contraseña actual incorrecta", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/auth/validate": {"get": {"tags": ["Autenticación"], "summary": "Validar token", "description": "Valida el token JWT actual y retorna información del usuario.", "operationId": "validateToken", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Token válido", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponse"}}}}, "401": {"description": "Token inválido o expirado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/auth/sessions": {"get": {"tags": ["Autenticación"], "summary": "Obtener información de sesiones activas", "description": "Obtiene información básica de las sesiones activas del usuario. Los administradores pueden solicitar información detallada usando el parámetro 'detailed=true'.", "operationId": "getActiveSessions", "security": [{"BearerAuth": []}], "parameters": [{"name": "detailed", "in": "query", "description": "Incluir información detallada de sesiones (solo para administradores)", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Información de sesiones obtenida exitosamente", "content": {"application/json": {"schema": {"type": "object", "properties": {"hasMobileSession": {"type": "boolean", "description": "Indica si el usuario tiene una sesión móvil activa"}, "hasWebSession": {"type": "boolean", "description": "Indica si el usuario tiene una sesión web activa"}, "totalActiveSessions": {"type": "integer", "description": "Número total de sesiones activas"}, "detailedSessions": {"type": "array", "items": {"$ref": "#/components/schemas/DetailedSessionInfo"}, "description": "Información detallada de sesiones (solo para administradores)"}, "error": {"type": "string", "description": "Error al obtener información detallada"}}}, "examples": {"basic": {"summary": "Información básica de sesiones", "value": {"hasMobileSession": true, "hasWebSession": false, "totalActiveSessions": 1}}, "detailed": {"summary": "Información detallada (admin)", "value": {"hasMobileSession": true, "hasWebSession": false, "totalActiveSessions": 1, "detailedSessions": [{"username": "john.doe", "sessionType": "MOBILE", "clientIpAddress": "*************", "userAgent": "Mozilla/5.0...", "sessionCreatedAt": "2024-01-15T10:30:00", "sessionExpiresAt": "2024-01-22T10:30:00", "lastActivityAt": "2024-01-15T14:20:00", "sessionId": "550e8400-e29b-41d4-a716-446655440000", "expired": false, "timeToExpirationSeconds": 604800}]}}}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado para información detallada (se requiere rol de administrador)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/auth/users/fullnames": {"post": {"tags": ["Autenticación"], "summary": "Obtener nombres completos por usernames", "description": "Obtiene los nombres completos de usuarios a partir de sus usernames. Los usuarios no encontrados no se incluyen en la respuesta.", "operationId": "getFullNamesByUsernames", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FullNameRequest"}, "examples": {"multiple-users": {"summary": "Múl<PERSON>les usernames", "value": {"usernames": ["jdoe", "msmith", "agarcia"]}}, "single-user": {"summary": "Un solo username", "value": {"usernames": ["jdoe"]}}}}}}, "responses": {"200": {"description": "Nombres completos obtenidos exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FullNameResponse"}, "examples": {"multiple-matches": {"summary": "<PERSON><PERSON><PERSON><PERSON> nombres encontrados", "value": {"fullNames": {"jdoe": "<PERSON>", "msmith": "<PERSON>", "agarcia": "<PERSON>"}}}, "partial-matches": {"summary": "Algunos usernames no encontrados", "value": {"fullNames": {"jdoe": "<PERSON>", "agarcia": "<PERSON>"}}}, "no-matches": {"summary": "Ningún username encontrado", "value": {"fullNames": {}}}}}}}, "400": {"description": "Request inválido (lista vacía o demasiados usernames)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token de autenticación"}}, "schemas": {"LoginRequest": {"type": "object", "required": ["identifier", "password", "sessionType"], "properties": {"identifier": {"type": "string", "description": "Email o nombre de usuario", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "minLength": 6, "description": "Contraseña del usuario", "example": "password123"}, "sessionType": {"type": "string", "enum": ["MOBILE", "WEB"], "description": "Tipo de sesión a crear"}, "pushToken": {"type": "string", "description": "Token para notificaciones push (solo sesiones móviles)", "example": "fcm-token-example"}, "deviceType": {"type": "string", "enum": ["ANDROID", "IOS"], "description": "Tipo de dispositivo móvil"}, "deviceId": {"type": "string", "description": "Identificador único del dispositivo", "example": "device-123"}}}, "RefreshTokenRequest": {"type": "object", "required": ["refreshToken"], "properties": {"refreshToken": {"type": "string", "description": "Token de renovación", "example": "eyJhbGciOiJIUzI1NiIs..."}}}, "ChangePasswordRequest": {"type": "object", "required": ["currentPassword", "newPassword", "confirmNewPassword"], "properties": {"currentPassword": {"type": "string", "format": "password", "description": "Contraseña actual del usuario"}, "newPassword": {"type": "string", "format": "password", "minLength": 6, "description": "Nueva contraseña"}, "confirmNewPassword": {"type": "string", "format": "password", "description": "Confirmación de la nueva contraseña"}}}, "AuthResponse": {"type": "object", "required": ["accessToken", "refreshToken", "tokenType", "expiresIn", "user"], "properties": {"accessToken": {"type": "string", "description": "Token JWT de acceso", "example": "eyJhbGciOiJIUzI1NiIs..."}, "refreshToken": {"type": "string", "description": "Token para renovar el access token", "example": "eyJhbGciOiJIUzI1NiIs..."}, "tokenType": {"type": "string", "default": "Bearer", "description": "Tipo de <PERSON>"}, "expiresIn": {"type": "integer", "format": "int64", "description": "Tiempo de expiración del access token en segundos", "example": 3600}, "user": {"$ref": "#/components/schemas/UserProfileResponse"}}}, "UserProfileResponse": {"type": "object", "required": ["id", "email", "username", "firstName", "lastName", "role", "createdAt"], "properties": {"id": {"type": "integer", "format": "int64", "description": "ID único del usuario", "example": 1}, "email": {"type": "string", "format": "email", "description": "Email del usuario", "example": "<EMAIL>"}, "username": {"type": "string", "description": "Nombre de usuario único", "example": "john.doe"}, "firstName": {"type": "string", "description": "Nombre del usuario", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Apellido del usuario", "example": "<PERSON><PERSON>"}, "role": {"type": "string", "enum": ["USER", "ADMIN"], "description": "Rol del usuario en el sistema"}, "createdAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de creación del usuario", "example": "2024-01-01T12:00:00Z"}}}, "DetailedSessionInfo": {"type": "object", "required": ["username", "sessionType", "sessionCreatedAt", "sessionExpiresAt", "sessionId", "expired"], "properties": {"username": {"type": "string", "description": "Nombre de usuario", "example": "john.doe"}, "sessionType": {"type": "string", "enum": ["MOBILE", "WEB"], "description": "Tipo de sesión"}, "clientIpAddress": {"type": "string", "description": "Dirección IP del cliente", "example": "*************"}, "userAgent": {"type": "string", "description": "User agent del cliente", "example": "Mozilla/5.0..."}, "sessionCreatedAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de creación de la sesión"}, "sessionExpiresAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de expiración de la sesión"}, "lastActivityAt": {"type": "string", "format": "date-time", "description": "Última actividad de la sesión"}, "sessionId": {"type": "string", "description": "ID único de la sesión", "example": "550e8400-e29b-41d4-a716-446655440000"}, "expired": {"type": "boolean", "description": "Indica si la sesión ha expirado"}, "timeToExpirationSeconds": {"type": "integer", "format": "int64", "description": "Tiempo restante hasta la expiración en segundos"}}}, "MessageResponse": {"type": "object", "required": ["message"], "properties": {"message": {"type": "string", "description": "Mensaje de respuesta", "example": "Operación exitosa"}}}, "ErrorResponse": {"type": "object", "required": ["error"], "properties": {"error": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "Error de validación"}, "details": {"type": "object", "description": "Detalles adicionales del error"}}}, "FullNameRequest": {"type": "object", "required": ["usernames"], "properties": {"usernames": {"type": "array", "items": {"type": "string"}, "description": "Lista de usernames para obtener sus nombres completos", "minItems": 1, "maxItems": 100, "example": ["jdoe", "msmith", "agarcia"]}}}, "FullNameResponse": {"type": "object", "required": ["fullNames"], "properties": {"fullNames": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Mapeo de username a nombre completo (firstName + lastName)", "example": {"jdoe": "<PERSON>", "msmith": "<PERSON>", "agarcia": "<PERSON>"}}}}}}}