{"openapi": "3.0.3", "info": {"title": "GEDSYS Push Token Management API", "description": "API para gestión de tokens de notificaciones push en GEDSYS (solo sesiones móviles)", "version": "1.0.0", "contact": {"name": "GEDSYS Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080", "description": "<PERSON><PERSON><PERSON>"}], "tags": [{"name": "<PERSON><PERSON>", "description": "Gestión de tokens de notificaciones push (solo sesiones móviles)"}], "paths": {"/identity/api/v1/auth/push-token": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Registrar token push", "description": "Registra o actualiza un token de notificaciones push para el usuario autenticado. Solo disponible para usuarios con sesiones móviles activas.", "operationId": "registerPushToken", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushTokenRequest"}}}}, "responses": {"200": {"description": "Token push registrado exitosamente", "content": {"application/json": {"schema": {"type": "object", "required": ["message", "pushToken"], "properties": {"message": {"type": "string", "example": "Push token registered successfully"}, "pushToken": {"$ref": "#/components/schemas/PushTokenInfo"}}}}}}, "400": {"description": "Datos de entrada inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - solo disponible para sesiones móviles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Push tokens can only be managed from mobile sessions"}}}}}}, "get": {"tags": ["<PERSON><PERSON>"], "summary": "Obtener información del token push", "description": "Obtiene la información del token push del usuario autenticado. Solo disponible para usuarios con sesiones móviles activas.", "operationId": "getPushToken", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Información del token push obtenida exitosamente", "content": {"application/json": {"schema": {"oneOf": [{"type": "object", "required": ["message", "pushToken"], "properties": {"message": {"type": "string", "example": "Push token retrieved successfully"}, "pushToken": {"$ref": "#/components/schemas/PushTokenInfo"}}}, {"type": "object", "required": ["message"], "properties": {"message": {"type": "string", "example": "No push token registered"}}}]}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - solo disponible para sesiones móviles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Push tokens can only be managed from mobile sessions"}}}}}}, "put": {"tags": ["<PERSON><PERSON>"], "summary": "Actualizar token push", "description": "Actualiza un token push existente para el usuario autenticado. Solo disponible para usuarios con sesiones móviles activas.", "operationId": "updatePushToken", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushTokenRequest"}}}}, "responses": {"200": {"description": "Token push actualizado exitosamente", "content": {"application/json": {"schema": {"type": "object", "required": ["message", "pushToken"], "properties": {"message": {"type": "string", "example": "Push token updated successfully"}, "pushToken": {"$ref": "#/components/schemas/PushTokenInfo"}}}}}}, "400": {"description": "Datos de entrada inválidos o no existe token para actualizar", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"no-existing-token": {"summary": "No existe token para actualizar", "value": {"error": "No existing push token to update. Use POST to register a new token."}}, "invalid-data": {"summary": "<PERSON><PERSON>", "value": {"error": "Invalid device type"}}}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - solo disponible para sesiones móviles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Push tokens can only be managed from mobile sessions"}}}}}}, "delete": {"tags": ["<PERSON><PERSON>"], "summary": "Eliminar token push", "description": "Elimina el token push del usuario autenticado. Solo disponible para usuarios con sesiones móviles activas.", "operationId": "deletePushToken", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Token push eliminado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "examples": {"deleted": {"summary": "Token eliminado", "value": {"message": "Push token deleted successfully"}}, "not-found": {"summary": "Token no encontrado", "value": {"message": "No push token found to delete"}}}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - solo disponible para sesiones móviles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Push tokens can only be managed from mobile sessions"}}}}}}}, "/identity/api/v1/auth/push-token/exists": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Verificar existencia de token push", "description": "Verifica si el usuario autenticado tiene un token push registrado. Solo disponible para usuarios con sesiones móviles activas.", "operationId": "hasPushToken", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Verificación completada exitosamente", "content": {"application/json": {"schema": {"type": "object", "required": ["hasPushToken", "message"], "properties": {"hasPushToken": {"type": "boolean", "description": "Indica si el usuario tiene un token push registrado", "example": true}, "message": {"type": "string", "description": "Mensaje descriptivo del resultado", "example": "User has push token"}}}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - solo disponible para sesiones móviles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Push tokens can only be managed from mobile sessions"}}}}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token de autenticación"}}, "schemas": {"PushTokenRequest": {"type": "object", "required": ["pushToken", "deviceType", "deviceId"], "properties": {"pushToken": {"type": "string", "description": "Token de notificaciones push (FCM para Android, APNS para iOS)", "minLength": 1, "example": "fcm-token-example-long-string"}, "deviceType": {"type": "string", "enum": ["ANDROID", "IOS"], "description": "Tipo de dispositivo móvil"}, "deviceId": {"type": "string", "description": "Identificador único del dispositivo", "minLength": 1, "example": "device-uuid-12345"}}}, "PushTokenInfo": {"type": "object", "required": ["token", "deviceType", "deviceId", "createdAt"], "properties": {"token": {"type": "string", "description": "Token de notificaciones push", "example": "fcm-token-example-long-string"}, "deviceType": {"type": "string", "enum": ["ANDROID", "IOS"], "description": "Tipo de dispositivo móvil"}, "deviceId": {"type": "string", "description": "Identificador único del dispositivo", "example": "device-uuid-12345"}, "createdAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de creación del token", "example": "2024-01-01T12:00:00Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de última actualización del token", "example": "2024-01-01T12:00:00Z"}}}, "MessageResponse": {"type": "object", "required": ["message"], "properties": {"message": {"type": "string", "description": "Mensaje de respuesta", "example": "Operación exitosa"}}}, "ErrorResponse": {"type": "object", "required": ["error"], "properties": {"error": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "Error de validación"}, "details": {"type": "object", "description": "Detalles adicionales del error"}}}}}}