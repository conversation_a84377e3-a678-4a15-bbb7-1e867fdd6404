{"openapi": "3.0.3", "info": {"title": "GEDSYS Health Check API", "description": "API para verificaciones de salud del sistema GEDSYS", "version": "1.0.0", "contact": {"name": "GEDSYS Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080", "description": "<PERSON><PERSON><PERSON>"}], "tags": [{"name": "Health", "description": "Endpoints para verificaciones de salud del sistema"}], "paths": {"/identity/api/v1/health/status": {"get": {"tags": ["Health"], "summary": "Verificación básica de salud", "description": "Endpoint básico de verificación de salud del sistema. Disponible para todos los usuarios para verificar el estado del servicio.", "operationId": "getHealthStatus", "responses": {"200": {"description": "Sistema funcionando correctamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasicHealthResponse"}, "example": {"status": "UP", "timestamp": "2024-01-01T12:00:00Z", "uptime": "2h 30m 45s", "version": "1.0.0"}}}}, "503": {"description": "Sistema no disponible", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasicHealthResponse"}, "example": {"status": "DOWN", "timestamp": "2024-01-01T12:00:00Z", "error": "Database connection failed"}}}}, "500": {"description": "Error interno del servicio de salud", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorHealthResponse"}, "example": {"status": "DOWN", "error": "Health check service unavailable"}}}}}}}, "/identity/api/v1/health/detailed": {"get": {"tags": ["Health", "Admin"], "summary": "Verificación detallada de salud con métricas", "description": "Endpoint de verificación de salud detallada con información de componentes y métricas del sistema. Solo disponible para administradores.", "operationId": "getDetailedHealth", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Información detallada de salud obtenida exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetailedHealthResponse"}, "example": {"status": "UP", "timestamp": "2024-01-01T12:00:00Z", "uptime": "2h 30m 45s", "version": "1.0.0", "components": {"database": {"status": "UP", "responseTime": "15ms", "activeConnections": 5, "maxConnections": 20}, "jwt": {"status": "UP", "tokensIssued": 150, "tokensValidated": 2500}, "diskSpace": {"status": "UP", "free": "15.2 GB", "total": "50 GB"}}, "metrics": {"totalUsers": 150, "activeSessions": 45, "totalRequests": 10000, "averageResponseTime": "120ms"}}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "503": {"description": "Sistema no disponible", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetailedHealthResponse"}, "example": {"status": "DOWN", "timestamp": "2024-01-01T12:00:00Z", "components": {"database": {"status": "DOWN", "error": "Connection timeout"}, "jwt": {"status": "UP"}}}}}}, "500": {"description": "Error interno del servicio de salud", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorHealthResponse"}, "example": {"status": "DOWN", "error": "Health check service unavailable"}}}}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token de autenticación"}}, "schemas": {"BasicHealthResponse": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["UP", "DOWN"], "description": "Estado general del sistema"}, "timestamp": {"type": "string", "format": "date-time", "description": "Timestamp de la verificación"}, "uptime": {"type": "string", "description": "Tiempo que el sistema ha estado funcionando", "example": "2h 30m 45s"}, "version": {"type": "string", "description": "Versión de la aplicación", "example": "1.0.0"}, "error": {"type": "string", "description": "Mensaje de error si el estado es DOWN"}}}, "DetailedHealthResponse": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["UP", "DOWN"], "description": "Estado general del sistema"}, "timestamp": {"type": "string", "format": "date-time", "description": "Timestamp de la verificación"}, "uptime": {"type": "string", "description": "Tiempo que el sistema ha estado funcionando"}, "version": {"type": "string", "description": "Versión de la aplicación"}, "components": {"type": "object", "description": "Estado de componentes individuales", "properties": {"database": {"$ref": "#/components/schemas/DatabaseHealthComponent"}, "jwt": {"$ref": "#/components/schemas/JwtHealthComponent"}, "diskSpace": {"$ref": "#/components/schemas/DiskSpaceHealthComponent"}}}, "metrics": {"type": "object", "description": "Métricas del sistema", "properties": {"totalUsers": {"type": "integer", "description": "Total de usuarios registrados"}, "activeSessions": {"type": "integer", "description": "Sesiones activas actuales"}, "totalRequests": {"type": "integer", "description": "Total de requests procesados"}, "averageResponseTime": {"type": "string", "description": "Tiempo promedio de respuesta", "example": "120ms"}}}}}, "DatabaseHealthComponent": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["UP", "DOWN"], "description": "Estado de la base de datos"}, "responseTime": {"type": "string", "description": "Tiempo de respuesta de la base de datos", "example": "15ms"}, "activeConnections": {"type": "integer", "description": "Conexiones activas actuales"}, "maxConnections": {"type": "integer", "description": "Máximo número de conexiones"}, "error": {"type": "string", "description": "Mensaje de error si el estado es DOWN"}}}, "JwtHealthComponent": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["UP", "DOWN"], "description": "Estado del servicio JWT"}, "tokensIssued": {"type": "integer", "description": "Tokens emitidos en el período actual"}, "tokensValidated": {"type": "integer", "description": "Tokens validados en el período actual"}, "error": {"type": "string", "description": "Mensaje de error si el estado es DOWN"}}}, "DiskSpaceHealthComponent": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["UP", "DOWN"], "description": "Estado del espacio en disco"}, "free": {"type": "string", "description": "Espacio libre disponible", "example": "15.2 GB"}, "total": {"type": "string", "description": "Espacio total en disco", "example": "50 GB"}, "threshold": {"type": "string", "description": "Umbral de espacio mínimo", "example": "10%"}, "error": {"type": "string", "description": "Mensaje de error si el estado es DOWN"}}}, "ErrorHealthResponse": {"type": "object", "required": ["status", "error"], "properties": {"status": {"type": "string", "enum": ["DOWN"], "description": "Estado del sistema"}, "error": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "Health check service unavailable"}}}, "ErrorResponse": {"type": "object", "required": ["error"], "properties": {"error": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}}}}}}