{"openapi": "3.0.3", "info": {"title": "GEDSYS Admin User Management API", "description": "API para gestión administrativa de usuarios en GEDSYS", "version": "1.0.0", "contact": {"name": "GEDSYS Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080", "description": "<PERSON><PERSON><PERSON>"}], "tags": [{"name": "Administración", "description": "Endpoints para gestión administrativa de usuarios (requiere rol ADMIN)"}], "paths": {"/identity/api/v1/admin/users": {"get": {"tags": ["Administración"], "summary": "Listar usuarios con paginación", "description": "Obtiene una lista paginada de usuarios con filtros opcionales. Requiere rol de administrador.", "operationId": "getUsers", "security": [{"BearerAuth": []}], "parameters": [{"name": "email", "in": "query", "description": "Filtro por email (coincidencia parcial)", "required": false, "schema": {"type": "string", "example": "<EMAIL>"}}, {"name": "enabled", "in": "query", "description": "Filtro por estado habilitado", "required": false, "schema": {"type": "boolean"}}, {"name": "status", "in": "query", "description": "Filtro por estado del usuario", "required": false, "schema": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED"]}}, {"name": "role", "in": "query", "description": "Filtro por rol del usuario", "required": false, "schema": {"type": "string", "enum": ["USER", "ADMIN"]}}, {"name": "page", "in": "query", "description": "Número de página (base 0)", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}}, {"name": "size", "in": "query", "description": "Tamaño de página", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "sort", "in": "query", "description": "Criterios de ordenación (formato: campo,dirección)", "required": false, "schema": {"type": "string", "default": "createdAt,desc", "example": "email,asc"}}], "responses": {"200": {"description": "Lista de usuarios obtenida exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedUserResponse"}}}}, "400": {"description": "Parámetros de paginación inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Administración"], "summary": "Crear nuevo usuario", "description": "Crea un nuevo usuario en el sistema. Requiere rol de administrador.", "operationId": "createUser", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}, "responses": {"201": {"description": "Usuario creado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponse"}}}}, "400": {"description": "Datos de entrada inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "El usuario ya existe", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/admin/users/{userId}": {"get": {"tags": ["Administración"], "summary": "Obtener detalles de usuario por ID", "description": "Obtiene información detallada de un usuario específico incluyendo sesiones activas y tokens push.", "operationId": "getUserById", "security": [{"BearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "ID del usuario", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "Detalles del usuario obtenidos exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminUserResponse"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Administración"], "summary": "Actualizar usuario", "description": "Actualiza la información de un usuario existente. Si se cambia la contraseña, se invalidan todas las sesiones del usuario.", "operationId": "updateUser", "security": [{"BearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "ID del usuario", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "Usuario actualizado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponse"}}}}, "400": {"description": "Datos de entrada inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Administración"], "summary": "Eliminar usuario", "description": "Elimina un usuario del sistema. Invalida todas las sesiones y remueve tokens asociados.", "operationId": "deleteUser", "security": [{"BearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "ID del usuario", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "Usuario eliminado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"message": "User deleted successfully"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/admin/users/{userId}/deactivate": {"put": {"tags": ["Administración"], "summary": "Desactivar usuario", "description": "Cambia el estado del usuario a INACTIVE.", "operationId": "deactivateUser", "security": [{"BearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "ID del usuario", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "Usuario desactivado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"message": "User deactivated successfully"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/admin/users/{userId}/reactivate": {"put": {"tags": ["Administración"], "summary": "Reactivar usuario", "description": "Cambia el estado del usuario a ACTIVE.", "operationId": "reactivateUser", "security": [{"BearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "ID del usuario", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "Usuario reactivado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"message": "User reactivated successfully"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/admin/users/{userId}/enabled": {"patch": {"tags": ["Administración"], "summary": "Habilitar o deshabilitar usuario", "description": "Cambia el estado enabled del usuario. Si se deshabilita, se invalidan todas las sesiones.", "operationId": "setUserEnabled", "security": [{"BearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "ID del usuario", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["enabled"], "properties": {"enabled": {"type": "boolean", "description": "Estado habilitado del usuario"}}}}}}, "responses": {"200": {"description": "Estado del usuario cambiado exitosamente", "content": {"application/json": {"schema": {"type": "object", "required": ["message", "enabled"], "properties": {"message": {"type": "string", "example": "User enabled successfully"}, "enabled": {"type": "boolean", "example": true}}}}}}, "400": {"description": "Datos de entrada inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/admin/users/{userId}/invalidate-sessions": {"post": {"tags": ["Administración"], "summary": "Invalidar todas las sesiones de un usuario", "description": "Invalida todas las sesiones activas de un usuario específico.", "operationId": "invalidateUserSessions", "security": [{"BearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "ID del usuario", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "Sesiones invalidadas exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"message": "All user sessions invalidated successfully"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/admin/users/stats": {"get": {"tags": ["Administración"], "summary": "Obtener estadísticas de usuarios", "description": "Obtiene estadísticas generales sobre los usuarios del sistema.", "operationId": "getUserStats", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Estadísticas obtenidas exitosamente", "content": {"application/json": {"schema": {"type": "object", "required": ["totalUsers", "enabledUsers", "disabledUsers", "adminUsers", "regularUsers", "totalPushTokens"], "properties": {"totalUsers": {"type": "integer", "format": "int64", "description": "Total de usuarios en el sistema"}, "enabledUsers": {"type": "integer", "format": "int64", "description": "Usuarios habilitados"}, "disabledUsers": {"type": "integer", "format": "int64", "description": "Usuarios deshabilitados"}, "adminUsers": {"type": "integer", "format": "int64", "description": "Usuarios con rol de administrador"}, "regularUsers": {"type": "integer", "format": "int64", "description": "Usuarios con rol regular"}, "totalPushTokens": {"type": "integer", "format": "int64", "description": "Total de tokens push registrados"}}}, "example": {"totalUsers": 150, "enabledUsers": 140, "disabledUsers": 10, "adminUsers": 5, "regularUsers": 145, "totalPushTokens": 85}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token de autenticación"}}, "schemas": {"CreateUserRequest": {"type": "object", "required": ["email", "password", "firstName", "lastName"], "properties": {"email": {"type": "string", "format": "email", "description": "Email del usuario", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "minLength": 6, "description": "Contraseña del usuario"}, "firstName": {"type": "string", "minLength": 1, "description": "Nombre del usuario", "example": "<PERSON>"}, "lastName": {"type": "string", "minLength": 1, "description": "Apellido del usuario", "example": "<PERSON><PERSON>"}, "role": {"type": "string", "enum": ["USER", "ADMIN"], "description": "Rol del usuario", "default": "USER"}, "enabled": {"type": "boolean", "description": "Estado habilitado del usuario", "default": true}}}, "UpdateUserRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "Email del usuario"}, "password": {"type": "string", "format": "password", "minLength": 6, "description": "Nueva contraseña (opcional)"}, "firstName": {"type": "string", "minLength": 1, "description": "Nombre del usuario"}, "lastName": {"type": "string", "minLength": 1, "description": "Apellido del usuario"}, "role": {"type": "string", "enum": ["USER", "ADMIN"], "description": "Rol del usuario"}, "enabled": {"type": "boolean", "description": "Estado habilitado del usuario"}}}, "UserProfileResponse": {"type": "object", "required": ["id", "email", "username", "firstName", "lastName", "role", "status", "enabled", "createdAt"], "properties": {"id": {"type": "integer", "format": "int64", "description": "ID único del usuario"}, "email": {"type": "string", "format": "email", "description": "Email del usuario"}, "username": {"type": "string", "description": "Nombre de usuario único"}, "firstName": {"type": "string", "description": "Nombre del usuario"}, "lastName": {"type": "string", "description": "Apellido del usuario"}, "role": {"type": "string", "enum": ["USER", "ADMIN"], "description": "Rol del usuario"}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED"], "description": "Estado del usuario"}, "enabled": {"type": "boolean", "description": "Indica si el usuario está habilitado"}, "createdAt": {"type": "string", "format": "date-time", "description": "Fecha de creación del usuario"}}}, "PagedUserResponse": {"type": "object", "required": ["content", "totalElements", "totalPages", "size", "number", "numberOfElements", "first", "last"], "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/UserProfileResponse"}, "description": "Lista de usuarios de la página actual"}, "totalElements": {"type": "integer", "format": "int64", "description": "Total de elementos"}, "totalPages": {"type": "integer", "description": "Total de páginas"}, "size": {"type": "integer", "description": "Tamaño de página"}, "number": {"type": "integer", "description": "Número de página actual"}, "numberOfElements": {"type": "integer", "description": "Número de elementos en la página actual"}, "first": {"type": "boolean", "description": "Indica si es la primera página"}, "last": {"type": "boolean", "description": "Indica si es la última página"}, "empty": {"type": "boolean", "description": "Indica si la página está vacía"}}}, "AdminUserResponse": {"type": "object", "required": ["user", "activeSessions", "pushTokenInfo"], "properties": {"user": {"$ref": "#/components/schemas/UserProfileResponse"}, "activeSessions": {"type": "array", "items": {"$ref": "#/components/schemas/ActiveSessionInfo"}, "description": "Sesiones activas del usuario"}, "pushTokenInfo": {"allOf": [{"$ref": "#/components/schemas/PushTokenInfo"}], "nullable": true, "description": "Información del token push (puede ser null)"}}}, "ActiveSessionInfo": {"type": "object", "required": ["sessionType", "lastActivity", "expiryDate"], "properties": {"sessionType": {"type": "string", "enum": ["MOBILE", "WEB"], "description": "Tipo de sesión"}, "lastActivity": {"type": "string", "format": "date-time", "description": "Última actividad de la sesión"}, "expiryDate": {"type": "string", "format": "date-time", "description": "Fecha de expiración de la sesión"}}}, "PushTokenInfo": {"type": "object", "required": ["token", "deviceType", "deviceId", "createdAt"], "properties": {"token": {"type": "string", "description": "Token de notificaciones push"}, "deviceType": {"type": "string", "enum": ["ANDROID", "IOS"], "description": "Tipo de dispositivo"}, "deviceId": {"type": "string", "description": "ID único del dispositivo"}, "createdAt": {"type": "string", "format": "date-time", "description": "Fecha de creación del token"}}}, "MessageResponse": {"type": "object", "required": ["message"], "properties": {"message": {"type": "string", "description": "Mensaje de respuesta"}}}, "ErrorResponse": {"type": "object", "required": ["error"], "properties": {"error": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "details": {"type": "object", "description": "Detalles adicionales del error"}}}}}}