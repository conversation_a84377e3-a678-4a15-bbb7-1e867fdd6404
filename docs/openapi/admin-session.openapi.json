{"openapi": "3.0.3", "info": {"title": "GEDSYS Admin Session Management API", "description": "API para gestión administrativa de sesiones en GEDSYS", "version": "1.0.0", "contact": {"name": "GEDSYS Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080", "description": "<PERSON><PERSON><PERSON>"}], "tags": [{"name": "Administración de Sesiones", "description": "Endpoints para gestión administrativa de sesiones activas (requiere rol ADMIN)"}], "paths": {"/identity/api/v1/admin/sessions": {"get": {"tags": ["Administración de Sesiones"], "summary": "Obtener información detallada de sesiones activas", "description": "Obtiene información detallada de sesiones activas del sistema. Sin parámetro userId muestra todas las sesiones. Con userId muestra solo las sesiones de ese usuario. Solo disponible para administradores.", "operationId": "getActiveSessions", "security": [{"BearerAuth": []}], "parameters": [{"name": "userId", "in": "query", "description": "ID del usuario para filtrar sesiones (opcional)", "required": false, "schema": {"type": "integer", "format": "int64", "minimum": 1}, "example": 123}], "responses": {"200": {"description": "Información de sesiones obtenida exitosamente", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DetailedSessionInfo"}}, "examples": {"all-sessions": {"summary": "Todas las sesiones del sistema", "value": [{"username": "john.doe", "sessionType": "MOBILE", "clientIpAddress": "*************", "userAgent": "MyApp/1.0 (Android 12)", "sessionCreatedAt": "2024-01-15T10:30:00", "sessionExpiresAt": "2024-01-22T10:30:00", "lastActivityAt": "2024-01-15T14:20:00", "sessionId": "550e8400-e29b-41d4-a716-446655440000", "expired": false, "timeToExpirationSeconds": 604800}, {"username": "jane.smith", "sessionType": "WEB", "clientIpAddress": "*************", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "sessionCreatedAt": "2024-01-15T09:00:00", "sessionExpiresAt": "2024-01-15T12:00:00", "lastActivityAt": "2024-01-15T11:45:00", "sessionId": "660f9511-f40c-52e5-b827-557766551111", "expired": false, "timeToExpirationSeconds": 900}]}, "user-sessions": {"summary": "Sesiones de un usuario específico", "value": [{"username": "john.doe", "sessionType": "MOBILE", "clientIpAddress": "*************", "userAgent": "MyApp/1.0 (Android 12)", "sessionCreatedAt": "2024-01-15T10:30:00", "sessionExpiresAt": "2024-01-22T10:30:00", "lastActivityAt": "2024-01-15T14:20:00", "sessionId": "550e8400-e29b-41d4-a716-446655440000", "expired": false, "timeToExpirationSeconds": 604800}]}}}}}, "400": {"description": "Parámetros <PERSON>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/identity/api/v1/admin/sessions/{sessionId}": {"delete": {"tags": ["Administración de Sesiones"], "summary": "Invalidar sesión específica por ID de sesión", "description": "Invalida una sesión específica utilizando su ID de sesión. Esta acción terminará inmediatamente la sesión especificada sin afectar otras sesiones del mismo usuario. Solo disponible para administradores.", "operationId": "invalidateSession", "security": [{"BearerAuth": []}], "parameters": [{"name": "sessionId", "in": "path", "required": true, "description": "ID de la sesión a invalidar", "schema": {"type": "string", "minLength": 1}, "example": "550e8400-e29b-41d4-a716-446655440000"}], "responses": {"200": {"description": "Sesión invalidada exitosamente", "content": {"application/json": {"schema": {"type": "object", "required": ["message", "sessionId"], "properties": {"message": {"type": "string", "example": "Session invalidated successfully"}, "sessionId": {"type": "string", "description": "ID de la sesión invalidada (puede estar abreviado para logs)", "example": "550e8400..."}}}}}}, "400": {"description": "ID de sesión inválido o sesión no encontrada", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"empty-session-id": {"summary": "ID de sesión vacío", "value": {"error": "Session ID cannot be null or empty"}}, "session-not-found": {"summary": "Sesión no encontrada", "value": {"error": "Session not found or already expired"}}}}}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Error interno del servidor", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Failed to invalidate session"}}}}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token de autenticación"}}, "schemas": {"DetailedSessionInfo": {"type": "object", "required": ["username", "sessionType", "sessionCreatedAt", "sessionExpiresAt", "sessionId", "expired"], "properties": {"username": {"type": "string", "description": "Nombre de usuario propietario de la sesión", "example": "john.doe"}, "sessionType": {"type": "string", "enum": ["MOBILE", "WEB"], "description": "Tipo de sesión"}, "clientIpAddress": {"type": "string", "description": "Dirección IP del cliente (puede ser null)", "example": "*************", "nullable": true}, "userAgent": {"type": "string", "description": "User agent del cliente (puede ser null)", "example": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "nullable": true}, "sessionCreatedAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de creación de la sesión", "example": "2024-01-15T10:30:00"}, "sessionExpiresAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de expiración de la sesión", "example": "2024-01-22T10:30:00"}, "lastActivityAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de última actividad de la sesión (puede ser null)", "example": "2024-01-15T14:20:00", "nullable": true}, "sessionId": {"type": "string", "description": "Identificador único de la sesión", "example": "550e8400-e29b-41d4-a716-446655440000"}, "expired": {"type": "boolean", "description": "Indica si la sesión ha expirado", "example": false}, "timeToExpirationSeconds": {"type": "integer", "format": "int64", "description": "Tiempo restante hasta la expiración en segundos (puede ser null si expirado)", "example": 604800, "nullable": true}}}, "ErrorResponse": {"type": "object", "required": ["error"], "properties": {"error": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "Access denied"}, "details": {"type": "object", "description": "Detalles adicionales del error", "nullable": true}}}}}}