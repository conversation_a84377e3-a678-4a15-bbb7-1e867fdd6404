# PRD: Password Recovery Component - Authentication Provider

## 1. Información General

### 1.1 Título del Requerimiento
**Password Recovery Component**

### 1.2 Descripción
Componente de recuperación de contraseñas que genera códigos de verificación seguros y gestiona el proceso de cambio de contraseña. Este componente expone APIs para ser consumidas por servicios externos que manejarán la comunicación con usuarios.

### 1.3 Objetivos
- Proporcionar un mecanismo seguro de generación de códigos de recuperación
- Validar y procesar cambios de contraseña mediante códigos
- Mantener la trazabilidad de intentos de recuperación
- Ofrecer una API reutilizable para sistemas externos

## 2. User Stories

| ID | Como | Quiero | Para |
|----|------|--------|------|
| US-01 | Sistema Consumidor | Generar un código de recuperación para un usuario | Permitir el proceso de recuperación de contraseña |
| US-02 | Sistema Consumidor | Validar un código y cambiar la contraseña | Completar el proceso de recuperación |

## 3. Especificaciones Funcionales

### 3.1 Flujo de Recuperación

```mermaid
sequenceDiagram
    participant SC as Sistema Consumidor
    participant PRC as Password Recovery Controller
    participant VS as Validation Service
    participant CS as Code Service
    participant DB as Database
    participant AS as Audit Service

    Note over SC: Inicia recuperación
    SC->>PRC: POST /{identifier}/code
    PRC->>VS: Valida usuario existe
    VS->>DB: Query usuario (email/username)
    DB-->>VS: Datos usuario
    
    alt Usuario existe y activo
        VS->>CS: Solicita generación código
        CS->>CS: Genera código alfanumérico aleatorio
        CS->>DB: Almacena PasswordRecoveryCode
        CS->>AS: Registra SecurityAuditLog
        PRC-->>SC: 201 Created + código
    else Usuario no existe o inactivo
        PRC-->>SC: 404 Not Found
    end

    Note over SC: Sistema externo envía email

    Note over SC: Usuario proporciona código
    SC->>PRC: PATCH /{identifier}
    PRC->>VS: Valida código y datos
    VS->>DB: Verifica PasswordRecoveryCode
    
    alt Código válido y no expirado
        VS->>DB: Actualiza User.password
        VS->>DB: Marca PasswordRecoveryCode.used = true
        VS->>AS: Registra SecurityAuditLog
        PRC-->>SC: 200 OK
    else Código inválido/expirado/usado
        VS->>DB: Marca PasswordRecoveryCode.used = true
        VS->>AS: Registra SecurityAuditLog
        PRC-->>SC: 400 Bad Request
    end
```

### 3.2 Reglas de Negocio

| Regla | Descripción |
|-------|-------------|
| RN-01 | El código de recuperación tiene validez configurable (default: **60 minutos**) |
| RN-02 | El código se invalida después de **1 intento** (exitoso o fallido) |
| RN-03 | El código es **alfanumérico de 8 caracteres** generado aleatoriamente |
| RN-04 | Solo se puede tener **1 código activo** por usuario |
| RN-05 | Al generar un nuevo código, se invalidan los anteriores |
| RN-06 | Todos los intentos se registran automáticamente en auditoría |
| RN-07 | El componente **NO** envía comunicaciones externas |
| RN-08 | Solo usuarios con status **ACTIVE** pueden recuperar contraseña |

## 4. Especificaciones Técnicas

### 4.1 API Endpoints

#### Password Recovery Controller

| Método | Endpoint | Descripción |
|--------|----------|-------------|
| POST | `/api/v1/password-recovery/{identifier}/code` | Genera código de recuperación |
| PATCH | `/api/v1/password-recovery/{identifier}` | Valida código y cambia contraseña |

### 4.2 Schemas

#### Response: Code Generation (POST /{identifier}/code)
```json
{
  "success": true,
  "data": {
    "code": "A1B2C3D4",
    "email": "<EMAIL>",
    "expiresAt": "2025-09-24T15:00:00Z",
    "createdAt": "2025-09-24T14:00:00Z"
  }
}
```

#### Request: Password Reset (PATCH /{identifier})
```json
{
  "code": "string",         // código de recuperación
  "newPassword": "string"   // nueva contraseña
}
```

#### Response: Password Reset Success
```json
{
  "success": true,
  "message": "Contraseña actualizada exitosamente",
  "timestamp": "2025-09-24T14:00:00Z"
}
```

#### Response: Error
```json
{
  "success": false,
  "error": {
    "code": "INVALID_CODE",
    "message": "El código proporcionado no es válido",
    "timestamp": "2025-09-24T14:00:00Z"
  }
}
```

### 4.3 Modelo de Datos

```mermaid
erDiagram
    USER {
        bigint id PK
        varchar email UK
        varchar username UK
        varchar password
        varchar first_name
        varchar last_name
        enum status
        enum role
        boolean enabled
        timestamp created_at
        timestamp updated_at
    }
    
    PASSWORD_RECOVERY_CODE {
        bigint id PK
        bigint user_id FK
        varchar code UK
        timestamp created_at
        timestamp expires_at
        boolean used
        timestamp used_at
        varchar request_ip
        varchar usage_ip
        varchar session_id
    }
    
    SECURITY_AUDIT_LOG {
        bigint id PK
        bigint user_id FK
        enum event_type
        varchar event_description
        json event_details
        varchar ip_address
        varchar user_agent
        timestamp created_at
    }
    
    USER ||--o{ PASSWORD_RECOVERY_CODE : generates
    USER ||--o{ SECURITY_AUDIT_LOG : triggers
```

### 4.4 Entidad JPA - PasswordRecoveryCode

```java
@Entity
@Table(name = "password_recovery_codes")
public class PasswordRecoveryCode {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(unique = true, nullable = false, length = 8)
    private String code;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;
    
    @Column(nullable = false)
    private boolean used = false;
    
    @Column(name = "used_at")
    private LocalDateTime usedAt;
    
    @Column(name = "request_ip", length = 45)
    private String requestIp;
    
    @Column(name = "usage_ip", length = 45)
    private String usageIp;
    
    @Column(name = "session_id", length = 100)
    private String sessionId;
}
```

### 4.5 Enum - SecurityEventType

```java
public enum SecurityEventType {
    PASSWORD_RECOVERY_REQUESTED,
    PASSWORD_RECOVERY_CODE_GENERATED,
    PASSWORD_RECOVERY_SUCCESS,
    PASSWORD_RECOVERY_FAILED,
    PASSWORD_RECOVERY_EXPIRED,
    PASSWORD_RECOVERY_RATE_LIMITED
}
```

### 4.6 Códigos de Error

| Código | HTTP Status | Descripción |
|--------|------------|-------------|
| `USER_NOT_FOUND` | 404 | Usuario no existe |
| `USER_INACTIVE` | 403 | Usuario con status != ACTIVE |
| `INVALID_CODE` | 400 | Código inválido o no existe |
| `EXPIRED_CODE` | 400 | Código expirado |
| `USED_CODE` | 400 | Código ya utilizado |
| `RATE_LIMIT_EXCEEDED` | 429 | Demasiados intentos |

## 5. Configuración

### 5.1 Propiedades del Componente (application.yml)
```yaml
password-recovery:
  code:
    expiration-minutes: 60  # Tiempo de expiración en minutos (default: 60)
```

## 6. Consideraciones de Seguridad

| Aspecto | Implementación |
|---------|----------------|
| **Rate Limiting** | Máximo 3 solicitudes por usuario cada 15 minutos |
| **Código Seguro** | Generación aleatoria con `SecureRandom` |
| **Almacenamiento** | Códigos hasheados con BCrypt en base de datos |
| **Auditoría** | Registro automático en SecurityAuditLog |
| **Validación Usuario** | Solo usuarios con UserStatus.ACTIVE |

## 7. Integración con Sistema Existente

### 7.1 Dependencias con Entidades Existentes

- **User**: Entidad principal, se valida por `email` o `username`
- **RefreshToken**: Se invalidan todos los tokens al cambiar contraseña
- **SecurityAuditLog**: Nueva entidad para auditoría de seguridad

### 7.2 Responsabilidades del Sistema Consumidor

El sistema que consume este componente debe:
- Validar la identidad inicial del usuario
- Gestionar el envío del código por el canal correspondiente
- Manejar la interfaz de usuario
- Implementar reintentos y manejo de errores
- Gestionar las plantillas de comunicación
- Aplicar políticas de contraseña si las requiere

## 8. Criterios de Aceptación

- [ ] Genera códigos únicos alfanuméricos de 8 caracteres aleatorios
- [ ] Códigos expiran según configuración (default 60 minutos)
- [ ] Códigos se invalidan después de cualquier intento de uso
- [ ] Solo permite 1 código activo por usuario
- [ ] Valida que el usuario tenga status ACTIVE
- [ ] Invalida todos los RefreshToken al cambiar contraseña
- [ ] Registra automáticamente todos los eventos en SecurityAuditLog
- [ ] API responde en menos de 500ms
- [ ] Implementa rate limiting (3 intentos/15 minutos)

## 9. Dependencias

- Spring Security (existente)
- Spring Data JPA (existente)
- BCrypt (existente)
- Base de datos PostgreSQL (existente)
- SecureRandom para generación de códigos

## 10. Fuera de Alcance

- Envío de correos electrónicos
- Gestión de plantillas
- Interfaz de usuario
- Notificaciones push
- Validación de políticas de contraseña
- Recuperación por SMS
- Preguntas de seguridad
- Enlaces de recuperación (magic links)
- Soporte multi-idioma

## 11. Métricas de Éxito

| Métrica | Objetivo |
|---------|----------|
| Disponibilidad del servicio | > 99.9% |
| Tiempo de respuesta P95 | < 500ms |
| Tasa de generación exitosa | > 99% |
| Códigos comprometidos | 0% |
| Eventos auditados | 100% |

## 12. Ejemplos de Uso

### Generar Código
```bash
POST /api/v1/password-recovery/<EMAIL>/code

Response: 201 Created
{
  "success": true,
  "data": {
    "code": "A1B2C3D4",
    "email": "<EMAIL>",
    "expiresAt": "2025-09-24T15:00:00Z",
    "createdAt": "2025-09-24T14:00:00Z"
  }
}
```

### Cambiar Contraseña
```bash
PATCH /api/v1/password-recovery/<EMAIL>

Request Body:
{
  "code": "A1B2C3D4",
  "newPassword": "NuevaContraseña123!"
}

Response: 200 OK
{
  "success": true,
  "message": "Contraseña actualizada exitosamente",
  "timestamp": "2025-09-24T14:00:00Z"
}
```

### Error - Código Expirado
```bash
PATCH /api/v1/password-recovery/<EMAIL>

Request Body:
{
  "code": "X9Y8Z7W6",
  "newPassword": "NuevaContraseña123!"
}

Response: 400 Bad Request
{
  "success": false,
  "error": {
    "code": "EXPIRED_CODE",
    "message": "El código de recuperación ha expirado",
    "timestamp": "2025-09-24T14:00:00Z"
  }
}
```

---

**Versión:** 3.1  
**Fecha:** 2025-09-24  
**Estado:** Pendiente de Aprobación  
**Cambios:**
- Simplificada configuración: solo tiempo de expiración en minutos
- Código generado aleatoriamente sin configuración
- Auditoría automática sin configuración
- Removida validación de políticas de contraseña